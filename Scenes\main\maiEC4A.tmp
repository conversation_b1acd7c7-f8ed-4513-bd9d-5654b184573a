[gd_scene load_steps=5 format=3 uid="uid://chgwxhrtw1lln"]

[ext_resource type="PackedScene" uid="uid://ceo44f3u14hlm" path="res://Scenes/maps/test_map.tscn" id="1_vg3wp"]
[ext_resource type="Texture2D" uid="uid://83kim8fm0f2s" path="res://Assets/turrets/techno turret2.png" id="2_4q1tb"]
[ext_resource type="Script" path="res://Scenes/ui/hud.gd" id="2_cy82p"]
[ext_resource type="Script" path="res://Scenes/ui/turret_drag_texture.gd" id="3_bl72m"]

[node name="main" type="Node2D"]

[node name="TestMap" parent="." instance=ExtResource("1_vg3wp")]

[node name="Camera2D" type="Camera2D" parent="."]

[node name="UI" type="CanvasLayer" parent="."]

[node name="HUD" type="Control" parent="UI"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
script = ExtResource("2_cy82p")

[node name="Turrets" type="HBoxContainer" parent="UI/HUD"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -24.0
grow_horizontal = 2
grow_vertical = 0

[node name="TestTurret" type="PanelContainer" parent="UI/HUD/Turrets"]
custom_minimum_size = Vector2(64, 64)
layout_mode = 2

[node name="TextureRect" type="TextureRect" parent="UI/HUD/Turrets/TestTurret"]
texture_filter = 1
custom_minimum_size = Vector2(64, 64)
layout_mode = 2
mouse_filter = 0
texture = ExtResource("2_4q1tb")
script = ExtResource("3_bl72m")

[node name="HBoxContainer" type="HBoxContainer" parent="UI/HUD"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -40.0
offset_bottom = 40.0
grow_horizontal = 0

[node name="HPLabel" type="Label" parent="UI/HUD/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "HP: 20/20"
