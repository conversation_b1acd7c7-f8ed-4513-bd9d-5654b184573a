# 塔防游戏增强系统需求确认

## 原始需求
基于 requirement.md 的更新内容，实现塔防游戏的新功能增强。

## 已有实现检查
通过检查现有代码发现：
- ✅ DA/TA系统已实现 (`Scenes/effects/da_ta_effect.gd`)
- ✅ 元素属性系统已实现 (ENHANCEMENT_SYSTEM.md)
- ✅ 被动协同系统已实现 (`Scenes/systems/PassiveSynergyManager.gd`)
- ❌ 充能系统未实现
- ❌ 召唤石系统未实现
- ❌ 新炮塔类型未实现
- ❌ 怪物技能系统未实现

## 澄清后的需求 (质量评分: 90/100)

### 需要实现的功能：

#### 1. 新炮塔类型 (8种)
- 箭塔：有弹道，伤害低，速度快
- 捕获塔：有弹道，AOE，伤害低，速度慢，命中单位速度降低100%，持续1.5S
- 法师塔：有弹道，AOE，伤害高，速度慢
- 感应塔：无伤害，侦测隐身，降低隐身单位40%移动速度
- 末日塔：无弹道，单体持续高伤害15s，禁用对方所有技能，CD 20s
- 脉冲塔：无弹道，周期性对攻击范围内所有敌方造成伤害，速度中，伤害中
- 弹射塔：有弹道，伤害低，速度中，子弹会在敌方一定距离内弹射，最多5次
- 光环塔：无弹道，持续对攻击范围内所有敌方速度降低30%
- 虚弱塔：有弹道，伤害低，速度快，命中单位降低5%防御力，可叠加，最多10层，持续5S

#### 2. 炮塔被动增益属性
- 箭塔：攻击范围里每有一个捕获塔，DA +10%， TA+5%
- 捕获塔：攻击范围里的所有友方塔增加 10% 攻速
- 法师塔：每有一个其他法师塔，自身伤害+10%
- 末日塔：每有一个塔造成TA，CD-0.5S
- 脉冲塔：左右2边塔获得狂躁增益，攻速+5%，伤害+5%
- 弹射塔：仅有一个弹射塔时，攻击+50%
- 光环塔：上下2边塔获得幸运增益，DA+15%，TA+10%
- 虚弱塔：对减速敌人伤害增加15%

#### 3. DA/TA系统扩展
- 充能不会重置DA/TA概率
- 被动加成相加叠加
- 实时更新机制已确认

#### 4. 怪物技能系统
- 霜冻光环：降低范围内所有塔攻击速度20%，CD回复速度降低20% (不可驱散)
- 加速：随机友方速度增加50%，持续2s，CD 5s (可驱散)
- 自爆：HP低于10%时，开始1s读条自爆，如果成功，眩晕范围内塔1.5S (不可驱散)
- 石化：自身获得防御力+500% buff 3s，CD 7秒 (可驱散)

#### 5. 防御力系统
- 公式：受到伤害 = 原始伤害 * 属性克制倍率 / (1+防御力/100)

#### 6. 召唤石系统
- 最多装备3只召唤石
- 通过掉落物合成获得
- 3个水平格子UI
- 直接显示CD状态（参考WOW）
- 召唤石类型：
  - 湿婆：1次性，所有塔攻击力+150%，持续15S
  - 路西法：圆形范围内共造成2000点光属性伤害，CD 2min
  - 欧罗巴：圆形范围内共造成1200点冰属性伤害，并冻结所有单位2s，CD 3min
  - 泰坦：对所有塔充能30，伤害增加30%，持续5S，CD 2min
  - 宙斯：驱散范围内敌方的BUFF，造成1500点光属性伤害，CD 3min

#### 7. 炮塔充能条系统
- 上限100充能值
- 每次攻击后增加充能值
- 满了之后触发充能技
- 攻击类技能无法触发DA/TA
- 充能技能无冷却时间
- 需要解锁科技树
- 充能技能：
  - 箭塔：[剑雨] 小范围AOE，在目标区域施放15支箭，对所有单位造成伤害
  - 捕获塔：[刺网] 捕获网范围增加100%，被捕单位防御力降低15%
  - 法师塔：[激活] 攻击速度增加30%，持续3S

## 技术实现优先级
1. 新炮塔类型和数值
2. 炮塔被动增益系统
3. 充能系统
4. 怪物技能系统
5. 召唤石系统

## 集成需求
- 不需要与设置系统集成
- 需要与现有元素系统、DA/TA系统、被动协同系统兼容
- 需要创建科技树解锁机制

## 确认质量评分: 90/100
- ✅ 功能规格明确
- ✅ 技术集成要求清晰
- ✅ 实现优先级确定
- ✅ 与现有系统兼容性考虑