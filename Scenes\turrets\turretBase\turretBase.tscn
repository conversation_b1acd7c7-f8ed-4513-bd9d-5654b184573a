[gd_scene load_steps=5 format=3 uid="uid://b35vf52g3e7yc"]

[ext_resource type="Script" uid="uid://dsx3ft7vk8ed8" path="res://Scenes/turrets/turretBase/turret_base.gd" id="1_l4mpo"]
[ext_resource type="Texture2D" uid="uid://83kim8fm0f2s" path="res://Assets/turrets/technoturret.png" id="2_alg2e"]

[sub_resource type="CircleShape2D" id="CircleShape2D_aicnu"]
radius = 22.8

[sub_resource type="CircleShape2D" id="CircleShape2D_kae7m"]
radius = 230.11

[node name="Turret" type="Node2D"]
script = ExtResource("1_l4mpo")

[node name="Sprite2D" type="Sprite2D" parent="."]
z_index = 2
texture_filter = 1
texture = ExtResource("2_alg2e")

[node name="CollisionArea" type="Area2D" parent="."]
collision_layer = 2
collision_mask = 2

[node name="CollisionShape2D" type="CollisionShape2D" parent="CollisionArea"]
shape = SubResource("CircleShape2D_aicnu")

[node name="DetectionArea" type="Area2D" parent="."]
collision_layer = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="DetectionArea"]
shape = SubResource("CircleShape2D_kae7m")

[node name="AttackCooldown" type="Timer" parent="."]
autostart = true

[connection signal="input_event" from="CollisionArea" to="." method="_on_collision_area_input_event"]
[connection signal="area_entered" from="DetectionArea" to="." method="_on_detection_area_area_entered"]
[connection signal="area_exited" from="DetectionArea" to="." method="_on_detection_area_area_exited"]
[connection signal="timeout" from="AttackCooldown" to="." method="attack"]
