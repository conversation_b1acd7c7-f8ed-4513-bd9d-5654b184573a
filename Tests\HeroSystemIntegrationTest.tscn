[gd_scene load_steps=3 format=3 uid="uid://d8e9f0a1b2c3"]

[ext_resource type="Script" path="res://Tests/HeroSystemIntegrationTest.gd" id="1_integration"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.1, 0.1, 0.1, 0.9)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[node name="HeroSystemIntegrationTest" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_integration")

[node name="Panel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="StatusLabel" type="Label" parent="Panel"]
layout_mode = 1
anchors_preset = 12
anchor_top = 0.05
anchor_bottom = 0.15
offset_left = 10.0
offset_right = -10.0
text = "Hero System Integration Test"
horizontal_alignment = 1
font_size = 20

[node name="RunTestButton" type="Button" parent="Panel"]
layout_mode = 1
anchors_preset = 12
anchor_top = 0.15
anchor_bottom = 0.25
offset_left = 10.0
offset_right = -10.0
text = "Run All Tests"
font_size = 16

[node name="TestLog" type="TextEdit" parent="Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
offset_left = 10.0
offset_top = 60.0
offset_right = -10.0
offset_bottom = -10.0
theme_override_font_sizes/font_size = 12
editable = false
show_line_numbers = true
wrap_mode = 2