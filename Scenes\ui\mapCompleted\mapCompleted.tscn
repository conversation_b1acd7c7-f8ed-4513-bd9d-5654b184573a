[gd_scene load_steps=2 format=3 uid="uid://3ap3on04gj27"]

[ext_resource type="Script" uid="uid://blabny5bfwe15" path="res://Scenes/ui/mapCompleted/mapCompleted.gd" id="1_r4qa7"]

[node name="GameOverPanel" type="PanelContainer"]
modulate = Color(1, 1, 1, 0)
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_r4qa7")

[node name="CenterPanel" type="PanelContainer" parent="."]
unique_name_in_owner = true
custom_minimum_size = Vector2(200, 200)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4

[node name="VBoxContainer" type="VBoxContainer" parent="CenterPanel"]
layout_mode = 2
size_flags_vertical = 4
theme_override_constants/separation = 10

[node name="GameOverText" type="Label" parent="CenterPanel/VBoxContainer"]
layout_mode = 2
text = "Map Completed!
"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="CenterPanel/VBoxContainer"]
layout_mode = 2

[node name="RetryButton" type="Button" parent="CenterPanel/VBoxContainer"]
layout_mode = 2
text = "Restart"

[node name="MainMenuButton" type="Button" parent="CenterPanel/VBoxContainer"]
layout_mode = 2
text = "Main Menu"

[connection signal="pressed" from="CenterPanel/VBoxContainer/RetryButton" to="." method="_on_retry_button_pressed"]
[connection signal="pressed" from="CenterPanel/VBoxContainer/MainMenuButton" to="." method="_on_main_menu_button_pressed"]
