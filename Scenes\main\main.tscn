[gd_scene load_steps=7 format=3 uid="uid://chgwxhrtw1lln"]

[ext_resource type="Script" uid="uid://cfyfhy8nj7yr0" path="res://Scenes/main/main.gd" id="1_22s4b"]
[ext_resource type="Script" uid="uid://o6ifuvp8iee3" path="res://Scenes/ui/hud.gd" id="2_cy82p"]
[ext_resource type="PackedScene" uid="uid://d0otd33ubv3hq" path="res://Scenes/ui/turretUI/turret_buy_container.tscn" id="3_pljl8"]
[ext_resource type="Script" uid="uid://brmqp6wit161p" path="res://Scenes/ui/turretUI/turrets_panel.gd" id="3_ryupq"]
[ext_resource type="PackedScene" uid="uid://b7k8l9m0n4x" path="res://Scenes/ui/heroSystem/HeroSelection.tscn" id="4_hero_sel"]
[ext_resource type="PackedScene" uid="uid://c9d8e7f6a5b" path="res://Scenes/ui/heroSystem/HeroInfoPanel.tscn" id="5_hero_info"]

[node name="main" type="Node2D"]
script = ExtResource("1_22s4b")

[node name="Camera2D" type="Camera2D" parent="."]

[node name="UI" type="CanvasLayer" parent="."]

[node name="HUD" type="Control" parent="UI"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
script = ExtResource("2_cy82p")

[node name="TurretsPanel" type="PanelContainer" parent="UI/HUD"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -64.0
grow_horizontal = 2
grow_vertical = 0
script = ExtResource("3_ryupq")

[node name="Turrets" type="HBoxContainer" parent="UI/HUD/TurretsPanel"]
layout_mode = 2

[node name="TurretBuyContainer" parent="UI/HUD/TurretsPanel/Turrets" instance=ExtResource("3_pljl8")]
layout_mode = 2

[node name="WaveWaitTimer" type="Timer" parent="UI/HUD"]

[node name="VBoxContainer" type="VBoxContainer" parent="UI/HUD"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -79.0
offset_bottom = 50.0
grow_horizontal = 0

[node name="HBoxContainer" type="HBoxContainer" parent="UI/HUD/VBoxContainer"]
layout_mode = 2

[node name="GoldLabel" type="Label" parent="UI/HUD/VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2

[node name="HPLabel" type="Label" parent="UI/HUD/VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "HP: 20/20"

[node name="WaveLabel" type="Label" parent="UI/HUD/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
horizontal_alignment = 1

[node name="RemainLabel" type="Label" parent="UI/HUD/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
horizontal_alignment = 1

# Hero System UI Components
[node name="HeroSelection" parent="UI" instance=ExtResource("4_hero_sel")]
visible = false

[node name="HeroInfoPanel" parent="UI" instance=ExtResource("5_hero_info")]
visible = false


[connection signal="timeout" from="UI/HUD/WaveWaitTimer" to="UI/HUD" method="_on_wave_wait_timer_timeout"]