# 塔防游戏需求文档

## 1. 炮塔系统

### 1.1 炮塔类型
- **箭塔**：有弹道，伤害低，速度快
- **捕获塔**：有弹道，AOE，伤害低，速度慢，命中单位速度降低100%，持续1.5s
- **法师塔**：有弹道，AOE，伤害高，速度慢
- **感应塔**：无伤害，侦测隐身，降低隐身单位40%移动速度
- **末日塔**：无弹道，单体持续高伤害15s，禁用对方所有技能，CD 20s
- **脉冲塔**：无弹道，周期性对攻击范围内所有敌方造成伤害，速度中，伤害中
- **弹射塔**：有弹道，伤害低，速度中，子弹会在敌方一定距离内弹射，最多5次
- **光环塔**：无弹道，持续对攻击范围内所有敌方速度降低30%
- **虚弱塔**：有弹道，伤害低，速度快，命中单位降低5%防御力，可叠加，最多10层，持续5s

### 1.2 炮塔被动增益系统
- **箭塔**：攻击范围里每有一个捕获塔，DA +10%，TA +5%
- **捕获塔**：攻击范围里的所有友方塔增加10%攻速
- **法师塔**：每有一个其他法师塔，自身伤害+10%
- **末日塔**：每有一个塔造成TA，CD -0.5s
- **脉冲塔**：左右2边塔获得狂躁增益，攻速+5%，伤害+5%
- **弹射塔**：仅有一个弹射塔时，攻击+50%
- **光环塔**：上下2边塔获得幸运增益，DA +15%，TA +10%
- **虚弱塔**：对减速敌人伤害增加15%

### 1.3 DA/TA攻击系统
- **DA (Double Attack)**：概率攻击2下，仅对弹道塔有效
- **TA (Triple Attack)**：概率攻击3下，仅对弹道塔有效

### 1.4 充能技能系统
- **充能条**：上限100，每次攻击后增加充能值
- **箭塔充能**：[剑雨] 小范围AOE，在目标区域施放15支箭
- **捕获塔充能**：[刺网] 捕获网范围增加100%，被捕单位防御力降低15%
- **法师塔充能**：[激活] 攻击速度增加30%，持续3s

## 2. 怪物系统

### 2.1 怪物技能系统
- **霜冻光环**：降低范围内所有塔攻击速度20%，CD回复速度降低20% (不可驱散)
- **加速**：随机友方速度增加50%，持续2s，CD 5s (可驱散)
- **自爆**：HP低于10%时，开始1s读条自爆，成功后眩晕范围内塔1.5s (不可驱散)
- **石化**：自身获得防御力+500% buff 3s，CD 7秒 (可驱散)

### 2.2 防御力系统
- **伤害计算公式**：受到伤害 = 原始伤害 × 属性克制倍率 ÷ (1 + 防御力/100)

## 3. 召唤石系统

### 3.1 召唤石规则
- 最多装备3只召唤石
- 部分召唤石有冷却时间，部分为一次性使用

### 3.2 召唤石列表
- **湿婆**：一次性，所有塔攻击力+150%，持续15s
- **路西法**：圆形范围内共造成2000点光属性伤害，CD 2分钟
- **欧罗巴**：圆形范围内共造成1200点冰属性伤害，并冻结所有单位2s，CD 3分钟
- **泰坦**：对所有塔充能30，伤害增加30%，持续5s，CD 2分钟
- **宙斯**：驱散范围内敌方的BUFF，造成1500点光属性伤害，CD 3分钟

## 4. 元素系统

### 4.1 元素类型
- **火元素**：高伤害，燃烧效果
- **冰元素**：减速，冻结效果
- **风元素**：击退，加速效果
- **土元素**：防御，减甲效果
- **光元素**：净化，治疗效果
- **暗元素**：腐蚀，生命偷取效果

### 4.2 元素克制关系
- 火克冰，冰克风，风克土
- 光克暗，暗克光
- 土克火

## 5. 宝石系统

### 5.1 宝石等级
- 1级宝石：基础效果
- 2级宝石：增强效果
- 3级宝石：高级效果

### 5.2 宝石效果
- **攻击宝石**：增加伤害
- **速度宝石**：增加攻击速度
- **范围宝石**：增加攻击范围
- **元素宝石**：赋予元素属性和特效

## 6. 英雄系统

### 6.1 英雄特性
- 英雄拥有独特技能和属性
- 英雄可以升级和装备道具
- 英雄拥有元素属性

### 6.2 英雄技能
- 主动技能：需要手动释放
- 被动技能：自动触发
- 天赋技能：永久加成

## 7. 科技树系统

### 7.1 科技分类
- 攻击科技：提升伤害相关属性
- 防御科技：提升防御相关属性
- 特殊科技：解锁特殊功能

### 7.2 科技升级
- 消耗科技点数升级
- 部分科技有前置条件

## 8. 性能优化需求

### 8.1 帧率优化
- 保持60FPS稳定运行
- 优化大量单位场景的性能

### 8.2 内存管理
- 对象池管理子弹和特效
- 及时清理无用对象

## 9. 用户界面需求

### 9.1 主界面
- 金币显示
- 生命值显示
- 波次信息
- 暂停/加速按钮

### 9.2 塔UI
- 塔的详细信息面板
- 升级界面
- 宝石镶嵌界面

### 9.3 其他UI
- 游戏设置界面
- 关卡选择界面
- 成就系统界面

## 10. 游戏平衡性

### 10.1 数值平衡
- 塔的攻击力、攻速、射程平衡
- 怪物血量、速度、防御平衡
- 经济系统平衡

### 10.2 策略深度
- 多种可行的通关策略
- 塔的组合搭配策略
- 元素克制策略运用