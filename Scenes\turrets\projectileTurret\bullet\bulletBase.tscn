[gd_scene load_steps=4 format=3 uid="uid://cw8dl6kw77qmf"]

[ext_resource type="Script" uid="uid://dr8irxwwp7t0t" path="res://Scenes/turrets/projectileTurret/bullet/bulletBase.gd" id="1_a00cp"]
[ext_resource type="SpriteFrames" uid="uid://djv5uvpfktdlq" path="res://Assets/bullets/bullet2.tres" id="2_otc75"]

[sub_resource type="CircleShape2D" id="CircleShape2D_govwh"]
radius = 4.5

[node name="BulletBase" type="Node2D"]
scale = Vector2(2, 2)
script = ExtResource("1_a00cp")

[node name="AnimatedSprite2D" type="AnimatedSprite2D" parent="."]
texture_filter = 1
sprite_frames = ExtResource("2_otc75")
autoplay = "default"

[node name="Area2D" type="Area2D" parent="."]
collision_layer = 8

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
shape = SubResource("CircleShape2D_govwh")

[node name="DisappearTimer" type="Timer" parent="."]
wait_time = 1.5
one_shot = true
autostart = true

[connection signal="area_entered" from="Area2D" to="." method="_on_area_2d_area_entered"]
[connection signal="timeout" from="DisappearTimer" to="." method="_on_disappear_timer_timeout"]
