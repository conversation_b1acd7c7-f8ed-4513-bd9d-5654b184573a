[gd_scene load_steps=3 format=3 uid="uid://c9d8e7f6a5b"]

[ext_resource type="Script" path="res://Scenes/ui/heroSystem/HeroInfoPanel.gd" id="1_4b6c"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.1, 0.1, 0.1, 0.8)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[node name="HeroInfoPanel" type="Control"]
layout_mode = 3
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_top = -150.0
offset_right = 300.0
offset_bottom = 0.0
script = ExtResource("1_4b6c")

[node name="Panel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="HeroNameLabel" type="Label" parent="Panel"]
layout_mode = 1
anchors_preset = 12
anchor_top = 0.05
anchor_bottom = 0.2
offset_left = 10.0
offset_right = -10.0
text = "英雄名称"
horizontal_alignment = 1
font_size = 18

[node name="LevelLabel" type="Label" parent="Panel"]
layout_mode = 1
anchors_preset = 12
anchor_top = 0.2
anchor_bottom = 0.3
offset_left = 10.0
offset_right = -10.0
text = "等级: 1"
horizontal_alignment = 1

[node name="HealthLabel" type="Label" parent="Panel"]
layout_mode = 1
anchors_preset = 12
anchor_top = 0.3
anchor_bottom = 0.4
offset_left = 10.0
offset_right = -10.0
text = "生命: 540/540"
horizontal_alignment = 1

[node name="StatsContainer" type="VBoxContainer" parent="Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
offset_left = 10.0
offset_top = 60.0
offset_right = -10.0
offset_bottom = -10.0

[node name="DamageLabel" type="Label" parent="Panel/StatsContainer"]
layout_mode = 2
text = "攻击力: 58"

[node name="DefenseLabel" type="Label" parent="Panel/StatsContainer"]
layout_mode = 2
text = "防御力: 10"

[node name="AttackSpeedLabel" type="Label" parent="Panel/StatsContainer"]
layout_mode = 2
text = "攻击速度: 0.9"

[node name="SkillsContainer" type="VBoxContainer" parent="Panel/StatsContainer"]
layout_mode = 2

[node name="SkillsTitle" type="Label" parent="Panel/StatsContainer/SkillsContainer"]
layout_mode = 2
text = "技能:"
font_size = 14
font_weight = 700

[node name="Skill1Label" type="Label" parent="Panel/StatsContainer/SkillsContainer"]
layout_mode = 2
text = "• 无影拳 (A)"

[node name="Skill2Label" type="Label" parent="Panel/StatsContainer/SkillsContainer"]
layout_mode = 2
text = "• 火焰甲 (B)"

[node name="Skill3Label" type="Label" parent="Panel/StatsContainer/SkillsContainer"]
layout_mode = 2
text = "• 末炎幻象 (C)"

[node name="ChargeBar" type="ProgressBar" parent="Panel/StatsContainer"]
layout_mode = 2
max_value = 100.0
value = 0.0
show_percentage = true

[node name="StatusLabel" type="Label" parent="Panel/StatsContainer"]
layout_mode = 2
text = "状态: 正常"
horizontal_alignment = 1