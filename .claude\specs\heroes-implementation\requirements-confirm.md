# 英雄实现需求确认文档

## 原始需求
"我在 heroes.txt 里更新了几个英雄， 你帮我实现"

## 澄清过程

### 用户回答的关键信息：
1. **英雄集成架构**: 已经集成过一个火系英雄，参考它
2. **自动攻击**: 你帮我设计，自动攻击  
3. **充能系统**: 参考之前的英雄，独立
4. **视觉和UI**: 占位符图形，自动，参考原有代码
5. **实现优先级**: 都实现，不用验证
6. **战场机制**: 怪物走的路径上。不可以，需要一个个放置，你帮我设计

## 最终确认需求

### 功能清晰度 (30/30分) ✅
- **英雄列表**: 三个火属性英雄需要实现
  - 霜火骑士：火属性，技能A(大字火)、B(冰霜路径)、C(熔岩武器)
  - 火石傀儡：火属性，技能A(震撼大地)、B(坚韧皮肤)、C(陨石冲击)  
  - 辉光仲裁史：火属性，技能A(神圣治愈)、B(烈火剑)、C(忠诚奖赏)
- **基础属性**: 参考现有英雄系统设计
- **自动攻击**: 自动锁定最近敌人并攻击

### 技术具体性 (25/25分) ✅
- **集成方式**: 基于现有 HeroBase.gd 架构
- **充能系统**: 独立充能，参考现有英雄实现
- **放置机制**: 在怪物路径上放置，一个个放置，需要设计放置系统
- **视觉资源**: 使用占位符图形

### 实现完整性 (25/25分) ✅
- **技能系统**: 每个英雄3个技能，包含伤害值、冷却时间、充能消耗
- **元素系统**: 火属性英雄，与现有元素系统集成
- **UI集成**: 参考原有代码自动生成
- **错误处理**: 遵循现有代码模式

### 业务背景 (20/20分) ✅
- **优先级**: 三个英雄都要实现
- **集成要求**: 与现有塔防游戏无缝集成
- **验证**: 不需要单独验证，直接实现全部

## 质量分数: 100/100 ✅

## 技术规范

### 英雄基础属性设计
基于现有HeroBase.gd，每个英雄包含：
- **生命值**: 300-500
- **攻击力**: 40-80  
- **攻击速度**: 1.0-1.5
- **攻击范围**: 150-200
- **充能上限**: 100
- **充能生成**: 2.0/秒

### 技能实现规范
- **A技能**: 低消耗(10-20充能)，短CD(6-8秒)
- **B技能**: 中消耗(20-40充能)，中CD(10-15秒) 
- **C技能**: 高消耗(50-60充能)，长CD(60-90秒)

### 放置系统设计
- **放置位置**: 仅限敌人行走路径上
- **放置限制**: 同时只能部署一个英雄
- **放置UI**: 类似塔的拖拽放置机制

## 实现计划

1. ✅ 分析现有英雄系统架构
2. 🔄 设计新英雄的基础属性  
3. ⏳ 实现霜火骑士技能系统
4. ⏳ 实现火石傀儡技能系统
5. ⏳ 实现辉光仲裁史技能系统
6. ⏳ 创建英雄放置系统
7. ⏳ 集成英雄管理UI

## 用户确认
需求已达到90+分质量标准，等待用户批准开始实现。