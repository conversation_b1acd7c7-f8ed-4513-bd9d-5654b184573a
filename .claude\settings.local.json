{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(find:*)", "<PERSON><PERSON>(touch:*)", "Bash(git add:*)", "Bash(git push:*)", "<PERSON><PERSON>(godot:*)", "Bash(rm:*)", "Bash(rg:*)", "<PERSON><PERSON>(mv:*)", "Bash(ls:*)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(echo:*)", "Bash(for:*)", "Bash(do echo \"Fixing $file\")", "Bash(done)", "Bash(do echo -n \"$file: \")", "Bash(cp:*)", "Bash(-exec head -n 500 {})", "<PERSON><PERSON>(claude mcp)", "Bash(git commit:*)", "Bash(git fetch:*)", "Bash(git rm:*)", "Bash(git rebase:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(dir)", "<PERSON><PERSON>(timeout 30 godot:*)", "<PERSON><PERSON>(timeout 15 godot:*)", "<PERSON><PERSON>(timeout 10 godot:*)", "<PERSON><PERSON>(timeout 5 godot:*)", "<PERSON><PERSON>(timeout:*)", "Bash(\"D:/Program Files/Godot_v4.4-stable_win64_console.exe\" --headless --path \"D:/pycode/Godot-4-Tower-Defense-Template\" \"D:/pycode/Godot-4-Tower-Defense-Template/project.godot\")", "Bash(where:*)", "Bash(\"D:/����/GODOT/godot.exe\":*)", "Bash(\"/d/工具/GODOT/godot.exe\":*)", "Bash(\"D:/工具/GODOT/godot.exe\":*)", "<PERSON><PERSON>(test:*)", "Bash(git checkout:*)"], "deny": []}}