# 三火属性英雄系统技术规范

## Problem Statement

- **Business Issue**: 需要实现三个新的火属性英雄（霜火骑士、火石傀儡、辉光仲裁史），每个英雄具有独特的A/B/C技能系统
- **Current State**: 已有基础英雄系统（HeroBase.gd）和一个幻影之灵英雄，需要扩展以支持新英雄
- **Expected Outcome**: 三个火属性英雄完全集成到现有游戏系统中，支持自动攻击、技能释放、路径部署和UI交互

## Solution Overview

- **Approach**: 基于现有HeroBase.gd架构，扩展Data.gd中的英雄数据定义，实现新技能系统，集成现有HeroManager部署机制
- **Core Changes**: 修改Data.gd添加英雄数据和技能定义，扩展HeroBase.gd的技能执行函数，确保与现有系统无缝集成
- **Success Criteria**: 三个英雄可正常部署、自动攻击、释放技能，符合现有游戏平衡性要求

## Technical Implementation

### Database Changes

#### 修改文件: `D:\pycode\Godot-4-Tower-Defense-Template\Scenes\main\Data.gd`

**新增英雄数据到 `heroes` 字典:**

```gdscript
# 在现有 heroes 字典中添加三个新英雄
"frost_fire_knight": {
	"name": "霜火骑士",
	"element": "fire",
	"base_stats": {
		"max_hp": 450,
		"damage": 65,
		"defense": 12,
		"attack_speed": 1.1,
		"attack_range": 180.0,
		"movement_speed": 0.0
	},
	"skills": ["fire_burst", "frost_path", "molten_weapon"],
	"sprite": "res://Assets/heroes/frost_fire_knight.png",
	"scene": "res://Scenes/heroes/HeroBase.tscn",
	"charge_generation": 2.0,
	"max_charge": 100,
	"description": "火系近战英雄，拥有火焰爆发和冰霜控制技能"
},
"fire_stone_golem": {
	"name": "火石傀儡",
	"element": "fire", 
	"base_stats": {
		"max_hp": 580,
		"damage": 48,
		"defense": 18,
		"attack_speed": 0.8,
		"attack_range": 160.0,
		"movement_speed": 0.0
	},
	"skills": ["earth_slam", "tough_skin", "meteor_impact"],
	"sprite": "res://Assets/heroes/fire_stone_golem.png",
	"scene": "res://Scenes/heroes/HeroBase.tscn",
	"charge_generation": 1.8,
	"max_charge": 100,
	"description": "火系坦克英雄，拥有地震和陨石技能"
},
"radiant_arbiter": {
	"name": "辉光仲裁史",
	"element": "fire",
	"base_stats": {
		"max_hp": 380,
		"damage": 52,
		"defense": 8,
		"attack_speed": 1.3,
		"attack_range": 200.0,
		"movement_speed": 0.0
	},
	"skills": ["holy_heal", "flame_sword", "loyalty_reward"],
	"sprite": "res://Assets/heroes/radiant_arbiter.png", 
	"scene": "res://Scenes/heroes/HeroBase.tscn",
	"charge_generation": 2.2,
	"max_charge": 100,
	"description": "火系支援英雄，拥有治疗和增益技能"
}
```

**新增技能数据到 `hero_skills` 字典:**

```gdscript
# 霜火骑士技能
"fire_burst": {
	"name": "大字火",
	"type": "A",
	"charge_cost": 15,
	"cooldown": 6.0,
	"cast_range": 0.0,
	"effect_radius": 150.0,
	"damage_base": 150,
	"damage_scaling": 0.8,
	"area_type": "quarter_circle",
	"element": "fire",
	"description": "前方1/4圆形区域喷射火焰，造成150点火伤害"
},
"frost_path": {
	"name": "冰霜路径",
	"type": "B",
	"charge_cost": 25,
	"cooldown": 12.0,
	"cast_range": 0.0,
	"effect_radius": 20.0,
	"slow_percentage": 80,
	"duration": 4.0,
	"ignore_immunity": true,
	"description": "在脚下放置冰墙，减速80%持续4秒，无视技能免疫"
},
"molten_weapon": {
	"name": "熔岩武器",
	"type": "C", 
	"charge_cost": 50,
	"cooldown": 90.0,
	"duration": 30.0,
	"melt_damage_per_second": 80,
	"melt_duration": 6.0,
	"freeze_combo_damage": 30,
	"freeze_combo_element": "ice",
	"description": "武器化为熔岩，攻击施加融化效果，配合冰属性产生汽化伤害"
},

# 火石傀儡技能
"earth_slam": {
	"name": "震撼大地",
	"type": "A",
	"charge_cost": 10,
	"cooldown": 6.0,
	"cast_range": 0.0,
	"effect_radius": 120.0,
	"damage_base": 100,
	"stun_duration": 1.5,
	"description": "践踏脚下范围，造成100点伤害和1.5秒眩晕"
},
"tough_skin": {
	"name": "坚韧皮肤",
	"type": "B",
	"charge_cost": 0,
	"cooldown": 0.0,
	"earth_heal_percentage": 10,
	"fire_kill_attack_bonus": 1,
	"passive": true,
	"description": "被动：土属性塔伤害时回复10%生命，火属性塔击杀时永久+1攻击"
},
"meteor_impact": {
	"name": "陨石冲击",
	"type": "C",
	"charge_cost": 60,
	"cooldown": 60.0,
	"cast_range": 250.0,
	"effect_radius": 180.0,
	"damage_per_second": 150,
	"duration": 8.0,
	"element": "fire",
	"description": "前方区域陨石雨，造成150/秒火伤害，持续8秒"
},

# 辉光仲裁史技能
"holy_heal": {
	"name": "神圣治愈",
	"type": "A",
	"charge_cost": 20,
	"cooldown": 7.0,
	"heal_amount": 200,
	"fire_light_bonus": 60,
	"target_type": "all_heroes",
	"description": "恢复所有英雄200生命，火/光英雄额外回复60点"
},
"flame_sword": {
	"name": "烈火剑",
	"type": "B",
	"charge_cost": 5,
	"cooldown": 0.0,
	"attack_count_trigger": 4,
	"da_attack_bonus": 60,
	"description": "每攻击4次，造成一次DA攻击，且增加60点攻击"
},
"loyalty_reward": {
	"name": "忠诚奖赏", 
	"type": "C",
	"charge_cost": 30,
	"cooldown": 6.0,
	"loyalty_duration": 5.0,
	"target_elements": ["fire", "light"],
	"guaranteed_da": true,
	"description": "每次触发DA时，所有火/光英雄获得忠诚BUFF，5秒内攻击必定DA"
}
```

### Code Changes

#### 修改文件: `D:\pycode\Godot-4-Tower-Defense-Template\Scenes\heroes\HeroBase.gd`

**新增技能执行函数到 `execute_skill_effects()` 方法:**

```gdscript
# 在 execute_skill_effects() 函数的 match 语句中添加新技能
func execute_skill_effects(skill: HeroSkill) -> void:
	if not skill:
		return
	
	var target_position = global_position
	
	match skill.skill_id:
		# 现有技能...
		"fire_burst":
			execute_fire_burst(skill, target_position)
		"frost_path":
			execute_frost_path(skill)
		"molten_weapon":
			execute_molten_weapon(skill)
		"earth_slam":
			execute_earth_slam(skill, target_position)
		"tough_skin":
			execute_tough_skin(skill)
		"meteor_impact":
			execute_meteor_impact(skill, target_position)
		"holy_heal":
			execute_holy_heal(skill)
		"flame_sword":
			execute_flame_sword(skill)
		"loyalty_reward":
			execute_loyalty_reward(skill)
		_:
			push_warning("Unknown skill: " + skill.skill_id)
```

**新增具体技能实现函数:**

```gdscript
# 霜火骑士技能实现
func execute_fire_burst(skill: HeroSkill, target_pos: Vector2) -> void:
	var skill_data = Data.hero_skills.get("fire_burst", {})
	var damage_base = skill_data.get("damage_base", 150)
	var damage_scaling = skill_data.get("damage_scaling", 0.8) 
	var effect_radius = skill_data.get("effect_radius", 150.0)
	
	var total_damage = damage_base + (current_stats.get("damage", 0) * damage_scaling)
	
	if gem_effect_system:
		var enemies = gem_effect_system.get_enemies_in_quarter_circle(target_pos, effect_radius, get_facing_direction())
		for enemy in enemies:
			if enemy.has_method("take_damage"):
				enemy.take_damage(total_damage, "fire")

func execute_frost_path(skill: HeroSkill) -> void:
	var skill_data = Data.hero_skills.get("frost_path", {})
	var effect_radius = skill_data.get("effect_radius", 20.0)
	var slow_percentage = skill_data.get("slow_percentage", 80)
	var duration = skill_data.get("duration", 4.0)
	
	if gem_effect_system:
		gem_effect_system.create_ice_wall(global_position, effect_radius, slow_percentage, duration)

func execute_molten_weapon(skill: HeroSkill) -> void:
	var skill_data = Data.hero_skills.get("molten_weapon", {})
	var duration = skill_data.get("duration", 30.0)
	
	if gem_effect_system:
		gem_effect_system.apply_effect(self, "molten_weapon", duration)
		set_meta("molten_weapon_active", true)
		set_meta("molten_weapon_timer", duration)

# 火石傀儡技能实现  
func execute_earth_slam(skill: HeroSkill, target_pos: Vector2) -> void:
	var skill_data = Data.hero_skills.get("earth_slam", {})
	var damage_base = skill_data.get("damage_base", 100)
	var effect_radius = skill_data.get("effect_radius", 120.0)
	var stun_duration = skill_data.get("stun_duration", 1.5)
	
	if gem_effect_system:
		var enemies = gem_effect_system.get_enemies_in_area(target_pos, effect_radius)
		for enemy in enemies:
			if enemy.has_method("take_damage"):
				enemy.take_damage(damage_base, "earth")
			if enemy.has_method("apply_effect"):
				enemy.apply_effect("stun", stun_duration)

func execute_tough_skin(skill: HeroSkill) -> void:
	# 被动技能，在其他地方处理效果
	if gem_effect_system:
		gem_effect_system.apply_effect(self, "tough_skin_passive", -1) # 永久效果

func execute_meteor_impact(skill: HeroSkill, target_pos: Vector2) -> void:
	var skill_data = Data.hero_skills.get("meteor_impact", {})
	var cast_range = skill_data.get("cast_range", 250.0)
	var effect_radius = skill_data.get("effect_radius", 180.0)
	var damage_per_second = skill_data.get("damage_per_second", 150)
	var duration = skill_data.get("duration", 8.0)
	
	# 获取前方目标位置
	var forward_direction = get_facing_direction()
	var meteor_position = target_pos + forward_direction * cast_range
	
	if gem_effect_system:
		gem_effect_system.create_meteor_storm(meteor_position, effect_radius, damage_per_second, duration)

# 辉光仲裁史技能实现
func execute_holy_heal(skill: HeroSkill) -> void:
	var skill_data = Data.hero_skills.get("holy_heal", {})
	var heal_amount = skill_data.get("heal_amount", 200)
	var fire_light_bonus = skill_data.get("fire_light_bonus", 60)
	
	var tree = get_tree()
	if tree and tree.current_scene:
		var heroes = tree.current_scene.get_tree().get_nodes_in_group("heroes")
		for hero in heroes:
			if hero.has_method("heal"):
				var total_heal = heal_amount
				if hero.element in ["fire", "light"]:
					total_heal += fire_light_bonus
				hero.heal(total_heal)

func execute_flame_sword(skill: HeroSkill) -> void:
	var skill_data = Data.hero_skills.get("flame_sword", {})
	
	# 增加攻击计数器
	var attack_count = get_meta("flame_sword_attacks", 0) + 1
	set_meta("flame_sword_attacks", attack_count)
	
	var trigger_count = skill_data.get("attack_count_trigger", 4)
	if attack_count >= trigger_count:
		# 触发DA攻击和攻击加成
		var da_bonus = skill_data.get("da_attack_bonus", 60)
		current_stats["damage"] += da_bonus
		set_meta("flame_sword_attacks", 0)
		
		# 触发DA攻击效果
		if attack_target and is_instance_valid(attack_target):
			var da_damage = current_stats.get("damage", 0) * 2.0
			attack_target.take_damage(da_damage, element)

func execute_loyalty_reward(skill: HeroSkill) -> void:
	var skill_data = Data.hero_skills.get("loyalty_reward", {})
	var duration = skill_data.get("loyalty_duration", 5.0)
	var target_elements = skill_data.get("target_elements", ["fire", "light"])
	
	var tree = get_tree()
	if tree and tree.current_scene:
		var heroes = tree.current_scene.get_tree().get_nodes_in_group("heroes")
		for hero in heroes:
			if hero.element in target_elements and gem_effect_system:
				gem_effect_system.apply_effect(hero, "loyalty_buff", duration)

# 辅助函数
func get_facing_direction() -> Vector2:
	# 简化实现，返回右方向，可以根据需要改进
	return Vector2.RIGHT

func heal(amount: float) -> void:
	if health_bar:
		var max_hp = current_stats.get("max_hp", 100)
		health_bar.value = min(health_bar.value + amount, max_hp)
```

#### 修改文件: `D:\pycode\Godot-4-Tower-Defense-Template\Scenes\heroes\HeroSkill.gd`

**扩展技能特殊需求检查:**

```gdscript
# 在 check_skill_specific_requirements() 函数中添加新技能检查
func check_skill_specific_requirements(hero: HeroBase) -> bool:
	match skill_id:
		# 现有技能检查...
		"tough_skin":
			# 被动技能始终可用
			return true
		"flame_sword":
			# 需要有攻击目标才能激活
			return hero.attack_target != null and is_instance_valid(hero.attack_target)
		"loyalty_reward":
			# 需要有其他火/光英雄才有意义
			return has_fire_or_light_heroes(hero)
		"meteor_impact":
			# 需要前方有效目标区域
			return has_valid_target_area(hero)
		_:
			return true

func has_fire_or_light_heroes(hero: HeroBase) -> bool:
	var tree = hero.get_tree()
	if not tree or not tree.current_scene:
		return false
	
	var heroes = tree.current_scene.get_tree().get_nodes_in_group("heroes")
	for other_hero in heroes:
		if other_hero != hero and other_hero.element in ["fire", "light"]:
			return true
	return false

func has_valid_target_area(hero: HeroBase) -> bool:
	# 简化实现，假设总是有有效目标区域
	return true
```

### API Changes

#### 新增全局效果系统扩展

**需要在GemEffectSystem中添加新效果支持:**

- `get_enemies_in_quarter_circle()` - 获取1/4圆形区域内的敌人
- `create_ice_wall()` - 创建冰墙减速效果
- `create_meteor_storm()` - 创建陨石雨效果
- `loyalty_buff` 效果 - 保证DA攻击的增益状态

### Configuration Changes

#### 修改游戏平衡参数

**基础属性设计原则:**
- **霜火骑士**: 平衡型近战，生命450，攻击65，防御12
- **火石傀儡**: 坦克型，生命580，攻击48，防御18  
- **辉光仲裁史**: 支援型，生命380，攻击52，防御8

**技能充能消耗:**
- A技能: 10-20充能，短冷却6-7秒
- B技能: 5-25充能，中冷却0-12秒
- C技能: 30-60充能，长冷却6-90秒

## Implementation Sequence

### Phase 1: 数据结构和基础集成
1. **修改Data.gd** - 添加三个新英雄数据定义和所有技能数据
2. **扩展HeroBase.gd** - 添加新技能执行函数框架
3. **测试基础部署** - 验证英雄可以正常创建和部署

### Phase 2: 核心技能实现
1. **实现霜火骑士技能** - `execute_fire_burst()`, `execute_frost_path()`, `execute_molten_weapon()`
2. **实现火石傀儡技能** - `execute_earth_slam()`, `execute_tough_skin()`, `execute_meteor_impact()`
3. **实现辉光仲裁史技能** - `execute_holy_heal()`, `execute_flame_sword()`, `execute_loyalty_reward()`
4. **扩展HeroSkill.gd** - 添加技能特殊需求检查

### Phase 3: 系统集成和优化
1. **集成GemEffectSystem** - 添加新效果支持函数
2. **测试技能交互** - 验证技能间配合效果
3. **UI集成测试** - 确保与现有HeroManager和UI系统正常工作
4. **性能优化** - 优化技能效果计算和渲染

每个阶段都应该是独立可部署和测试的。

## Validation Plan

### Unit Tests
- **英雄创建测试**: 验证三个新英雄可以正确从Data.gd创建
- **技能执行测试**: 验证每个技能的基础执行逻辑
- **充能系统测试**: 验证技能消耗和冷却机制
- **属性计算测试**: 验证伤害计算和效果应用

### Integration Tests  
- **部署系统测试**: 验证英雄可以在路径上正确部署
- **自动攻击测试**: 验证自动目标选择和攻击执行
- **技能自动释放测试**: 验证AI自动释放技能的优先级
- **多英雄协作测试**: 验证英雄间的增益和配合效果

### Business Logic Verification
- **游戏平衡性验证**: 确保新英雄不会破坏现有游戏平衡
- **技能效果验证**: 确保技能描述与实际效果一致
- **UI交互验证**: 确保玩家可以正常选择、部署和管理英雄
- **性能影响验证**: 确保新英雄不会造成明显的性能下降

## 关键约束条件

### MUST Requirements
- **完全兼容现有架构**: 不破坏现有HeroBase.gd和HeroManager.gd功能
- **路径部署限制**: 英雄只能部署在敌人行走路径附近（50像素内）
- **单英雄部署**: 同时只能部署一个英雄，符合现有游戏机制
- **自动攻击系统**: 英雄必须具备自动目标选择和攻击能力
- **技能优先级**: C > B > A 的自动释放优先级
- **元素系统集成**: 火属性伤害与现有元素系统完全兼容

### MUST NOT Requirements  
- **不修改核心架构**: 不改变HeroBase.gd的核心设计模式
- **不破坏现有英雄**: 确保幻影之灵等现有英雄正常工作
- **不影响性能**: 新技能不应导致显著的帧率下降
- **不添加复杂依赖**: 避免引入额外的外部依赖或复杂的状态管理

## 输出文件管理

### 主要修改文件
- **D:\pycode\Godot-4-Tower-Defense-Template\Scenes\main\Data.gd** - 英雄和技能数据定义
- **D:\pycode\Godot-4-Tower-Defense-Template\Scenes\heroes\HeroBase.gd** - 技能执行函数实现
- **D:\pycode\Godot-4-Tower-Defense-Template\Scenes\heroes\HeroSkill.gd** - 技能需求检查扩展

### 可能需要的新文件
- **占位符图形资源** - 如果现有占位符不够用
- **技能效果脚本** - 如果需要复杂的视觉效果

该技术规范提供了实现三个火属性英雄所需的完整蓝图，包括精确的数据结构、实现方法和集成点。所有修改都基于现有架构，确保无缝集成和最小的风险。