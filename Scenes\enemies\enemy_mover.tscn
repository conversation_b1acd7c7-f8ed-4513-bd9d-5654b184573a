[gd_scene load_steps=6 format=3 uid="uid://cb3v7e6vydle1"]

[ext_resource type="Script" uid="uid://ciftc55wet1op" path="res://Scenes/enemies/enemy_mover.gd" id="1_e187i"]
[ext_resource type="Texture2D" uid="uid://dhvmjhj28g7li" path="res://Assets/enemies/dino1.png" id="2_bpnqn"]

[sub_resource type="Animation" id="Animation_mhspp"]
resource_name = "move"
length = 0.6
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [4, 5, 6, 7, 8, 9]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_obqca"]
_data = {
&"move": SubResource("Animation_mhspp")
}

[sub_resource type="CircleShape2D" id="CircleShape2D_7h0ns"]
radius = 13.0

[node name="EnemyMover" type="PathFollow2D"]
loop = false
script = ExtResource("1_e187i")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
z_index = 5
scale = Vector2(2, 2)
texture = ExtResource("2_bpnqn")
hframes = 24
frame = 4

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
"": SubResource("AnimationLibrary_obqca")
}
autoplay = "move"

[node name="Area" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area"]
shape = SubResource("CircleShape2D_7h0ns")
