[gd_scene load_steps=2 format=3 uid="uid://i2phf2rir44j"]

[ext_resource type="Script" uid="uid://8cn1ioi72kbv" path="res://Scenes/ui/mainMenu/select_map_container.gd" id="1_32sip"]

[node name="SelectMapContainer" type="PanelContainer"]
custom_minimum_size = Vector2(250, 300)
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_32sip")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 2

[node name="Label" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Select Map"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="ScrollContainer" type="ScrollContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="MapsList" type="GridContainer" parent="VBoxContainer/ScrollContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 6
size_flags_vertical = 3
columns = 2
