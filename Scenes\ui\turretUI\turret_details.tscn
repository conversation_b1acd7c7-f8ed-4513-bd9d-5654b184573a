[gd_scene load_steps=5 format=3 uid="uid://dunjwx3q7asjq"]

[ext_resource type="Script" uid="uid://dvqdrraxry6ox" path="res://Scenes/ui/turretUI/turret_details.gd" id="1_0x613"]
[ext_resource type="Texture2D" uid="uid://83kim8fm0f2s" path="res://Assets/turrets/technoturret.png" id="1_1lsyn"]
[ext_resource type="PackedScene" uid="uid://bg1le57woay1j" path="res://Scenes/ui/turretUI/stat_label.tscn" id="3_bd6mv"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_vopfk"]

[node name="TurretDetails" type="PanelContainer"]
custom_minimum_size = Vector2(250, 250)
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -40.0
offset_top = -20.0
offset_bottom = 20.0
grow_horizontal = 0
grow_vertical = 2
script = ExtResource("1_0x613")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 2

[node name="PanelContainer" type="PanelContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="VBoxContainer" type="VBoxContainer" parent="VBoxContainer/PanelContainer"]
layout_mode = 2

[node name="TurretName" type="Label" parent="VBoxContainer/PanelContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Basic Turret"
horizontal_alignment = 1

[node name="TurretLevel" type="Label" parent="VBoxContainer/PanelContainer/VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
text = "Level 1
"
horizontal_alignment = 1

[node name="CloseButton" type="Button" parent="VBoxContainer/PanelContainer"]
layout_mode = 2
size_flags_horizontal = 0
text = "X"

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="Preview" type="PanelContainer" parent="VBoxContainer"]
custom_minimum_size = Vector2(64, 64)
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxEmpty_vopfk")

[node name="TurretTexture" type="TextureRect" parent="VBoxContainer/Preview"]
unique_name_in_owner = true
texture_filter = 1
layout_mode = 2
texture = ExtResource("1_1lsyn")
expand_mode = 3
stretch_mode = 5

[node name="Stats" type="GridContainer" parent="VBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_vertical = 3
columns = 2

[node name="StatLabel" parent="VBoxContainer/Stats" instance=ExtResource("3_bd6mv")]
unique_name_in_owner = true
layout_mode = 2

[node name="Controls" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="UpgradeButton" type="Button" parent="VBoxContainer/Controls"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "Upgrade"

[node name="SellButton" type="Button" parent="VBoxContainer/Controls"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
text = "Sell
"

[connection signal="pressed" from="VBoxContainer/PanelContainer/CloseButton" to="." method="_on_close_button_pressed"]
[connection signal="pressed" from="VBoxContainer/Controls/UpgradeButton" to="." method="_on_upgrade_button_pressed"]
[connection signal="pressed" from="VBoxContainer/Controls/SellButton" to="." method="_on_sell_button_pressed"]
