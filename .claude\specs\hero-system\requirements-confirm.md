# 英雄系统最终需求确认文档

## 原始需求
用户请求: "我增加了 hero 需求，你帮我实现"

## 需求澄清过程

### 第一轮澄清 (初始得分: 25/100)
**问题**: 英雄的基本概念和功能不明确
**用户回复**: 基本英雄概念（像塔放置、战斗技能、升级天赋系统等）

### 第二轮澄清 - 文档冲突解决 (冲突得分: 65/100)
**问题**: 现有hero_system.md文档与澄清信息存在冲突
**用户最终确认**:
1. 放置在路径中（阻挡敌人）有碰撞体积
2. 自动复活机制
3. 需要添加5选1选择系统 
4. 所有需求一起实现

## 最终确认需求 (得分: 95/100) ✅

### 功能清晰度 (29/30)
- **英雄定义**: 特殊强力近战单位，放置在路径中阻挡敌人
- **放置机制**: 在路径中放置，具有碰撞体积，阻挡敌人移动
- **战斗系统**: A/B/C技能系统，充能机制，优先释放大招
- **复活系统**: 死亡后10秒自动复活
- **选择机制**: 第1关随机5选1，每第5波再选择

### 技术特异性 (25/25)
- **集成方式**: 继承塔系统但特殊化为路径阻挡单位
- **技能系统**: A技能（小技能）、B技能（buff技能）、C技能（大招）
- **充能系统**: 技能消耗充能点，充能点自动恢复
- **升级系统**: 击杀敌人获得经验值升级，而非金币
- **天赋系统**: 5/10/15级2选1天赋，15级满级
- **DA/TA系统**: 继承现有伤害加成和塔攻击加成系统

### 实现完整性 (25/25)
- **核心英雄系统**: 英雄基类、属性系统、技能释放机制
- **选择系统**: 随机英雄池、5选1界面、波次触发逻辑
- **升级天赋系统**: 经验值计算、天赋树UI、属性加成
- **关卡词缀系统**: 随机词缀生成、效果应用、UI显示
- **信息集成面板**: 选中单位详细信息显示
- **可视化范围指示**: 放置时范围显示、光环效果可视化

### 业务背景 (16/20)
- **游戏价值**: 替换近战塔概念，增强策略深度
- **平衡考虑**: 路径阻挡 + 强力战斗能力的平衡
- **系统整合**: 与现有元素、塔防、波次系统深度整合

## 完整功能需求清单

### 1. 英雄核心系统
- **英雄基类**: 继承塔基类，特殊化为路径单位
- **属性系统**: HP/攻击力/防御力/攻速，元素属性
- **技能系统**: A/B/C三类技能，充能点机制
- **复活机制**: 死亡后10秒自动复活
- **碰撞系统**: 在路径中阻挡敌人移动

### 2. 英雄选择系统
- **随机池管理**: 英雄数据库和随机选择算法
- **5选1界面**: 选择UI，英雄预览和属性显示
- **波次触发**: 第1关和每第5波触发选择
- **限制管理**: 数量限制和替换机制

### 3. 升级和天赋系统
- **经验系统**: 击杀敌人获得经验，等级计算
- **天赋树**: 5/10/15级2选1天赋选择
- **属性成长**: 等级提升带来的属性加成
- **天赋效果**: 各种天赋的具体效果实现

### 4. 关卡词缀系统
- **词缀数据库**: 各种词缀效果定义
- **随机生成**: 每关1-2个词缀的随机选择
- **效果应用**: 词缀对敌人/塔/英雄的影响
- **UI显示**: 当前关卡词缀的显示界面

### 5. 信息集成面板
- **选择检测**: 鼠标选中塔/敌人/英雄的检测
- **信息面板**: 固定位置的详细信息显示
- **实时更新**: 血量、buff/debuff状态实时更新
- **多类型支持**: 塔、敌人、英雄的不同信息格式

### 6. 可视化范围指示
- **攻击范围**: 放置/选中时显示攻击范围圆圈
- **光环范围**: 光环技能的影响范围可视化
- **协同指示**: 能触发协同效应的塔高亮显示
- **交互反馈**: 范围重叠、有效位置的视觉反馈

## 示例英雄：幻影之灵（火）
- **基础属性**: HP540, 攻击58, 防御10, 攻速0.9s
- **A技能-无影拳**: 范围攻击，无敌状态，20充能，5s CD
- **B技能-火焰甲**: 防御提升+护罩+范围DOT，12s CD
- **C技能-末炎幻象**: 召唤幻象，献祭光环，60充能，90s CD

## 质量评估: 95/100 ✅

**完全符合实现标准** - 需求清晰完整，可以开始全功能实现。

## 输出要求
- 需求文档更新到现有的 `hero_system.md`
- 实现所有6大功能模块
- 与现有宝石效果系统、塔防系统完整集成